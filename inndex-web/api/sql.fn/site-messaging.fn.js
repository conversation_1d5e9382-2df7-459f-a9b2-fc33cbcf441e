const {
    runReadQuery,
    expandFilesReferences,
    escapeSearchString,
} = require('./core.fn');

const msg_type = {
    received: 'received',
    sent: 'sent',
}

const attachUserRefs = async(record,userIds)=>{
    let userDetails =  [];
    for (let recipient of record.recipients_array){
        let user = userIds.find(a=>a.id == recipient.user_ref);
        if(user){
            userDetails.push(user);
        }
    }
    record.users  = userDetails;
    return record;
};

const getProjectSiteMessagesSql = async({projectId, searchTerm, userIds, sortKey, sortDir, limit, offset}, countOnly = false)=>{
    let variables = [projectId];
    if(userIds&& userIds.length){
        variables.push(...userIds);
    }
    if(searchTerm){
        variables.push(`%${escapeSearchString(searchTerm)}%`);
    }
    if (!countOnly) {
        variables.push(limit, offset);
    }
    let variableIndex = 0;
    let sql = `
    SELECT ${countOnly ? 'COUNT(*) as total' : `
        psm.*,
        (CASE WHEN u.id IS NOT NULL THEN json_build_object('id', u.id,'first_name', u.first_name,'last_name', u.last_name,'name', u.first_name || COALESCE(' ' || u.middle_name, '') || ' ' || u.last_name,'parent_company',u.parent_company) ELSE NULL END) as user_ref
    `}
    FROM project_site_messaging psm
    LEFT JOIN users u ON psm.user_ref = u.id
    WHERE
        is_sent = true
        ${projectId ? `AND psm.project_ref = $${++variableIndex}` : ''}
        ${userIds.length ? `AND psm.user_ref IN (${userIds.map(() => '$' + (++variableIndex)).join(',')})` : ''}
        ${searchTerm ? `AND ((msg_title ILIKE $${++variableIndex}  OR msg_text ILIKE  $${variableIndex}  OR CAST(psm.record_id AS VARCHAR) ILIKE $${variableIndex} OR u.first_name ILIKE $${variableIndex} OR u.middle_name ILIKE $${variableIndex} OR u.last_name ILIKE $${variableIndex})
         OR (concat_ws(' ',u.first_name,(CASE WHEN middle_name = '' THEN NULL ELSE middle_name END), u.last_name) ILIKE $${variableIndex})) ` : ''}
        ${countOnly ? '' : `ORDER BY psm.${sortKey} ${sortDir} LIMIT $${++variableIndex} OFFSET $${++variableIndex}`}
`;

return {sql,variables};
};

const getCompanySiteMessagesSql = async ({
    employerId,
    searchTerm,
    sortKey,
    sortDir,
    limit,
    offset,
    senders,
    userId
}, countOnly = false) => {
    let variables = [];

    if(userId){
        variables.push(userId);
    }
    if(employerId){
        variables.push(employerId);
    }
    if (searchTerm) {
        variables.push(`%${escapeSearchString(searchTerm)}%`);
    }
    if (senders) {
        variables.push(...senders);
    }
    if (!countOnly) {
        variables.push(limit, offset);
    }

    let variableIndex = 0;
    let sql = `SELECT ${countOnly ? ' (COUNT(DISTINCT sm.id))::int as total ':` sm.*,
        COALESCE(
            ARRAY_AGG(
                JSON_BUILD_OBJECT(
                    'id', smr.id,
                    'msg_ref', smr.msg_ref,
                    'sent_at', smr.sent_at,
                    'accepted_at', smr.accepted_at,
                    'read_at', smr.read_at,
                    'user_ref', smr.user_ref
                )
            ), ARRAY[]::JSON[]
        ) AS recipients_array,
        JSON_BUILD_OBJECT(
            'email', u.email,
            'name', concat_ws(' ', u.first_name, u.middle_name, u.last_name),
            'id', u.id
        ) AS sender `}
        FROM
            public.site_message sm
        ${userId ? ' INNER JOIN ' : ' LEFT JOIN '}
            site_message_recipient smr ON sm.id = smr.msg_ref
            ${userId ? ` AND smr.user_ref = $${++variableIndex} ` : ``}
        JOIN users u on sm.sender_ref = u.id
        WHERE
            sm.company_ref = $${++variableIndex}
            AND deleted_on is null
            ${searchTerm ? `AND ((msg_title ILIKE $${++variableIndex}  OR msg_text ILIKE  $${variableIndex}  OR CAST(sm.record_id AS VARCHAR) ILIKE $${variableIndex})
            OR (concat_ws(' ',u.first_name,(CASE WHEN u.middle_name = '' THEN NULL ELSE u.middle_name END), u.last_name) ILIKE $${variableIndex})) ` : ''}
            ${(senders && senders.length) ? `AND sm.sender_ref in (${senders.map(() => `$${++variableIndex}`)}) `: '' }
        ${countOnly ? '' :
        `GROUP BY
            sm.id, u."createdAt", u."updatedAt", u."id"
        ORDER BY 
            sm.${sortKey} ${sortDir} LIMIT $${++variableIndex} OFFSET $${++variableIndex}`}`;

    return {
        sql,
        variables
    };
};


const attachProjectRef =async (records,projectDetails) =>{
    for(let record of records){
        record.project_ref = projectDetails;
    }
    return records;
}

module.exports = {
    getProjectSiteMessages : async (projectId,
        limit = 20,
        offset = 0,
        sortKey = 'id',
        sortDir = 'asc', {
            searchTerm = '',
            userIds,
        }) =>{
            let records=[],total=0;
            const  {sql:countSql,variables:countVariables} = await getProjectSiteMessagesSql({projectId,searchTerm,userIds,sortKey,sortDir,limit,offset},true);
            const countResult = await runReadQuery(countSql,countVariables,true)
            total = (countResult.total || 0);
            if(total){
                let{sql:selectSql,variables:selectVariables} = await getProjectSiteMessagesSql({projectId,searchTerm,userIds,sortKey,sortDir,limit,offset});
                records = await runReadQuery(selectSql, selectVariables);
                const project =  await sails.models.project_reader.findOne({id : projectId}).select(['id','name']);
                if(project){
                   records = await  attachProjectRef(records,project);
                }
            }
            return {total, records};
    },
    getCompanySiteMessages: async (employerId,
        limit = 20,
        offset = 0,
        sortKey = 'id',
        sortDir = 'asc', {
            searchTerm = '',
            senders = [],
            userId
        }) => {
        let records = [],
            total = 0,
            projectIds = [];
        const {
            sql: countSql,
            variables: countVariables
            } = await getCompanySiteMessagesSql({
                    employerId,
                    searchTerm,
                    projectIds,
                    senders,
                    userId
                }, true);
        const countResult = await runReadQuery(countSql, countVariables,true)
        total = (countResult.total || 0);
        if (total) {
            let {
                sql: selectSql,
                variables: selectVariables
            } = await getCompanySiteMessagesSql({
                employerId,
                searchTerm,
                projectIds,
                sortKey,
                sortDir,
                limit,
                offset,
                senders,
                userId
            });
            records = await runReadQuery(selectSql, selectVariables);
        }

        return {
            total,
            records
        };
    },
    updateResentData: async (message_id, userIds, resentBy, time) => {
        let rawResult = await sails.sendNativeQuery(`UPDATE site_message_recipient
            SET resent_at = resent_at ::jsonb || '{"id":${resentBy.id},"at":${time}}'::jsonb
            where user_ref in (${userIds.map(a=>+a)}) AND msg_ref = ${message_id} RETURNING *`);
            if(HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length){
                return rawResult.rows;
            }
            return null;
    },
    getConsolidatedMessagesListFn: async(userId, type = 'received', limit = 20, offset = 0, projectIds, companyIds, searchTerm= '') => {

        sails.log.info(`Fetching user ${type} messages for user: ${userId}`, `Project Ids: ${projectIds}, company Ids: ${companyIds}`, `limit: ${limit},`, `offset: ${offset}`,`searchTerm:${searchTerm}`);
        if(searchTerm){
            searchTerm = `'%${searchTerm}%'`
        }

        let sql =`WITH combined AS (
            SELECT
                sm.id,
                JSON_BUILD_OBJECT(
                    'id', emp.id,
                    'name', emp.name
                ) AS company_ref,
                sm.msg_title,
                sm.msg_text,
                sm.should_accepted,
                NULL AS project_ref,
                'company_message' AS source,
                JSON_BUILD_OBJECT(
                    'email', u.email,
                    'name', concat_ws(' ',u.first_name,(CASE WHEN u.middle_name = '' THEN NULL ELSE u.middle_name END), u.last_name),
                    'id', u.id,
                    'profile_pic_ref',JSON_BUILD_OBJECT( 
                        'id',uf.id,
                        'file_url',uf.file_url
                    ) 
                ) AS sender,
                sm."createdAt",
                NULL as read_by_recipients,
                smr.read_at as read_at 
            FROM
                site_message sm
            JOIN site_message_recipient smr ON sm.id = smr.msg_ref
                ${userId && type === msg_type.received ? `AND smr.user_ref = ${userId}` : ''}
            LEFT JOIN users u ON sm.sender_ref = u.id
            LEFT JOIN user_file uf on uf.id = u.profile_pic_ref
            LEFT JOIN employer emp ON sm.company_ref = emp.id 
            WHERE
                deleted_on IS NULL
                AND is_sent = TRUE
                ${(searchTerm && searchTerm.length) ? `AND ( sm.msg_text ILIKE ${searchTerm} OR sm.msg_title ILIKE ${searchTerm}
                    OR (concat_ws(' ',u.first_name,(CASE WHEN u.middle_name = '' THEN NULL ELSE u.middle_name END), u.last_name) ILIKE ${searchTerm}))`:``}
                ${companyIds && companyIds.length ? ` AND company_ref in (${companyIds.map(a => +a).join(',')})`:''}
                ${userId && type === msg_type.sent ? `AND sm.sender_ref = ${userId}` : ''}
                ${projectIds && projectIds.length ? `
                AND EXISTS (
                    SELECT 1
                    FROM json_array_elements_text(sm.projects::json) AS elem
                    WHERE elem::int = ANY(ARRAY[${projectIds.map(a => +a).join(',')}])
                )` : ''}
            GROUP BY
                sm.id, u."createdAt", u."updatedAt", u.id, emp.id, smr.read_at, uf.id
            UNION ALL
        
            SELECT
                psm.id,
                NULL AS company_ref,
                psm.msg_title,
                psm.msg_text,
                psm.should_accepted,
                JSON_BUILD_OBJECT(
                    'id', p.id,
                    'name', p.name,
                    'contractor', p.contractor

                ) as project_ref,
                'project_message' AS source,
                JSON_BUILD_OBJECT(
                    'email', u.email,
                    'name', concat_ws(' ',u.first_name,(CASE WHEN u.middle_name = '' THEN NULL ELSE u.middle_name END), u.last_name),
                    'id', u.id,
                    'profile_pic_ref',JSON_BUILD_OBJECT( 
                        'id',uf.id,
                        'file_url',uf.file_url
                    ) 
                ) AS sender,
                psm."createdAt",
                psm.read_by_recipients,
                null as read_at
            FROM
                project_site_messaging psm
            LEFT JOIN users u ON psm.user_ref = u.id
            LEFT JOIN user_file uf on uf.id = u.profile_pic_ref
            LEFT JOIN project p on p.id = psm.project_ref
             ${companyIds && companyIds.length? `AND p.parent_company in (${companyIds.map(a => +a).join(',')}) `: ''}
            WHERE
                is_sent = TRUE
                ${(searchTerm && searchTerm.length) ? `AND ( psm.msg_text ILIKE ${searchTerm} OR psm.msg_title ILIKE ${searchTerm}
                    OR (concat_ws(' ',u.first_name,(CASE WHEN u.middle_name = '' THEN NULL ELSE u.middle_name END), u.last_name) ILIKE ${searchTerm}))`:``}
                ${userId && type === msg_type.received ? `AND psm.recipients::jsonb @> '[${userId}]'::jsonb` : ''}
                ${userId && type === msg_type.sent ? `AND psm.user_ref = ${userId}` : ''}
                ${projectIds && projectIds.length ? `AND project_ref IN (${projectIds.map(a => +a).join(',')})` : ''}
            ),
            combined_ordered AS (
                SELECT
                    *,
                    ROW_NUMBER() OVER (ORDER BY "createdAt" DESC) AS row_num
                FROM
                    combined
            )
            SELECT
                id,
                company_ref,
                msg_title,
                msg_text,
                should_accepted,
                project_ref,
                source,
                sender,
                "createdAt",
                read_by_recipients,
                read_at
            FROM
                combined_ordered co
            ORDER BY
                row_num
            ${limit ? `LIMIT ${limit} OFFSET ${offset}` : ''};`;

        sails.log.info('[getConsolidatedMessagesList]',sql)

        return await runReadQuery(sql);
    },
    msg_type,
    getRecipientsList: async (message_id, userIds = [], expandProfilePicData = false, type = null) => {
        let query = `
                SELECT user_ref, first_name, last_name, read_at
                ${expandProfilePicData ?
                    `, JSON_BUILD_OBJECT(
                        'id', uf.id,
                        'file_url', uf.file_url,
                        'sm_url', uf.sm_url
                    ) as profile_pic_ref ` : ''}
                FROM site_message_recipient
                LEFT JOIN users u ON user_ref = u.id
                ${expandProfilePicData ? ` LEFT JOIN user_file uf on uf.id = u.profile_pic_ref `: ''}
                WHERE msg_ref = ${message_id}
                ${(userIds && userIds.length) ? ` AND user_ref IN (${userIds.map(a => +a)})` : ''}
                ${type ? ` AND type = '${type}'` : ''}
                ORDER BY u.first_name ASC
                `;


        let recipients = await runReadQuery(query);
        return recipients || [];
    },
    getMessageSendersList: async(tool, key, value) => {
        let sql = `select id, concat_ws(' ',first_name,middle_name,last_name) as name
            from users
            where id in (select distinct(user_ref) from ${tool} where ${key} = ${value})`;
        return await runReadQuery(sql);
    },
    getCombinedUnreadMessagesCountFn: async ( userId ) => {
        const variables = [userId];
    
        const companyMessagingUnreadCountSql = `
            SELECT COUNT(*)
            FROM site_message_recipient
            WHERE user_ref = $1 AND read_at IS NULL`;
    
        const projectSiteMessagingUnreadCountSql = `
            SELECT COUNT(*)
            FROM project_site_messaging
            WHERE recipients::JSONB @> jsonb_build_array($1::int)
            AND NOT (read_by_recipients::JSONB @> jsonb_build_array(jsonb_build_object('id', $1::int)));`;
    
        const [companyMessageUnreadCount, projectSiteMessagingUnreadCount] = await Promise.all([
            runReadQuery(companyMessagingUnreadCountSql, variables, true),
            runReadQuery(projectSiteMessagingUnreadCountSql, variables, true)
        ]);
    
        const totalCount = (+companyMessageUnreadCount.count) + (+projectSiteMessagingUnreadCount.count);
    
        return totalCount;
    }
};
