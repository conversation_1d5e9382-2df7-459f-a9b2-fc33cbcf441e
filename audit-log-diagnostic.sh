#!/bin/bash

# Audit Log Service Production Diagnostic Script
# Usage: ./audit-log-diagnostic.sh [service-url]

set -e

# Configuration
CLUSTER_NAME="inndex-prod-web-cluster"
SERVICE_NAME="audit-log-service-service"
LOG_GROUP="inndex-prod-audit-log-service"
REGION="eu-west-2"
SECRET_NAME="audit-log-service/production/env-list-Wixirg"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Service URL (can be passed as argument)
SERVICE_URL=${1:-"https://your-audit-log-service-url"}

echo -e "${YELLOW}=== Audit Log Service Production Diagnostic ===${NC}"
echo "Service URL: $SERVICE_URL"
echo "Region: $REGION"
echo ""

# Function to check command availability
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}❌ $1 is not installed${NC}"
        exit 1
    fi
}

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Check required tools
echo -e "${YELLOW}Checking required tools...${NC}"
check_command "aws"
check_command "curl"
check_command "jq"

# 1. Health Check
echo -e "\n${YELLOW}1. Testing Health Endpoint...${NC}"
if curl -f -s "$SERVICE_URL/health?from=diagnostic" > /dev/null 2>&1; then
    print_status 0 "Health endpoint is accessible"
    HEALTH_RESPONSE=$(curl -s "$SERVICE_URL/health?from=diagnostic")
    echo "Response: $HEALTH_RESPONSE"
else
    print_status 1 "Health endpoint is not accessible"
    echo "This indicates the service is not running or not reachable"
fi

# 2. Root Endpoint Check
echo -e "\n${YELLOW}2. Testing Root Endpoint...${NC}"
if curl -f -s "$SERVICE_URL/" > /dev/null 2>&1; then
    print_status 0 "Root endpoint is accessible"
    ROOT_RESPONSE=$(curl -s "$SERVICE_URL/")
    echo "Response: $ROOT_RESPONSE"
else
    print_status 1 "Root endpoint is not accessible"
fi

# 3. ECS Service Status
echo -e "\n${YELLOW}3. Checking ECS Service Status...${NC}"
ECS_STATUS=$(aws ecs describe-services \
    --cluster "$CLUSTER_NAME" \
    --services "$SERVICE_NAME" \
    --region "$REGION" \
    --query 'services[0].status' \
    --output text 2>/dev/null || echo "ERROR")

if [ "$ECS_STATUS" = "ACTIVE" ]; then
    print_status 0 "ECS service is ACTIVE"
    
    # Get running count
    RUNNING_COUNT=$(aws ecs describe-services \
        --cluster "$CLUSTER_NAME" \
        --services "$SERVICE_NAME" \
        --region "$REGION" \
        --query 'services[0].runningCount' \
        --output text)
    
    DESIRED_COUNT=$(aws ecs describe-services \
        --cluster "$CLUSTER_NAME" \
        --services "$SERVICE_NAME" \
        --region "$REGION" \
        --query 'services[0].desiredCount' \
        --output text)
    
    echo "Running tasks: $RUNNING_COUNT/$DESIRED_COUNT"
    
    if [ "$RUNNING_COUNT" -eq "$DESIRED_COUNT" ] && [ "$RUNNING_COUNT" -gt 0 ]; then
        print_status 0 "All desired tasks are running"
    else
        print_status 1 "Not all tasks are running"
    fi
else
    print_status 1 "ECS service status: $ECS_STATUS"
fi

# 4. Check Recent Logs
echo -e "\n${YELLOW}4. Checking Recent CloudWatch Logs...${NC}"
START_TIME=$(date -d '1 hour ago' +%s)000

LOG_EVENTS=$(aws logs filter-log-events \
    --log-group-name "$LOG_GROUP" \
    --start-time "$START_TIME" \
    --region "$REGION" \
    --query 'events[0:5].[timestamp,message]' \
    --output text 2>/dev/null || echo "ERROR")

if [ "$LOG_EVENTS" != "ERROR" ] && [ -n "$LOG_EVENTS" ]; then
    print_status 0 "Recent logs found"
    echo "Recent log entries:"
    echo "$LOG_EVENTS" | head -10
else
    print_status 1 "No recent logs found or log group doesn't exist"
fi

# 5. Check for Error Logs
echo -e "\n${YELLOW}5. Checking for Error Logs...${NC}"
ERROR_LOGS=$(aws logs filter-log-events \
    --log-group-name "$LOG_GROUP" \
    --start-time "$START_TIME" \
    --filter-pattern "ERROR" \
    --region "$REGION" \
    --query 'events[0:3].message' \
    --output text 2>/dev/null || echo "NONE")

if [ "$ERROR_LOGS" != "NONE" ] && [ -n "$ERROR_LOGS" ]; then
    print_status 1 "Error logs found"
    echo "Recent errors:"
    echo "$ERROR_LOGS"
else
    print_status 0 "No error logs found in the last hour"
fi

# 6. Check Environment Variables (from Secrets Manager)
echo -e "\n${YELLOW}6. Checking Environment Variables...${NC}"
SECRET_CHECK=$(aws secretsmanager describe-secret \
    --secret-id "$SECRET_NAME" \
    --region "$REGION" \
    --query 'Name' \
    --output text 2>/dev/null || echo "ERROR")

if [ "$SECRET_CHECK" != "ERROR" ]; then
    print_status 0 "Secrets Manager secret exists"
else
    print_status 1 "Secrets Manager secret not found or not accessible"
fi

# 7. Check Task Definition
echo -e "\n${YELLOW}7. Checking Task Definition...${NC}"
TASK_DEF=$(aws ecs describe-task-definition \
    --task-definition "audit-log-service-task-def" \
    --region "$REGION" \
    --query 'taskDefinition.status' \
    --output text 2>/dev/null || echo "ERROR")

if [ "$TASK_DEF" = "ACTIVE" ]; then
    print_status 0 "Task definition is active"
else
    print_status 1 "Task definition issue: $TASK_DEF"
fi

# Summary
echo -e "\n${YELLOW}=== Diagnostic Summary ===${NC}"
echo "If the health endpoint is not accessible but ECS shows the service as running,"
echo "check the following:"
echo "1. Load balancer configuration"
echo "2. Security group settings"
echo "3. Container port mapping"
echo "4. Environment variables in Secrets Manager"
echo ""
echo "If you see error logs, focus on:"
echo "1. DynamoDB table access"
echo "2. S3 bucket permissions"
echo "3. AWS credentials configuration"
echo ""
echo "For more detailed troubleshooting, see: troubleshoot-audit-log-service.md"
