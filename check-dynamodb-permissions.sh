#!/bin/bash

# DynamoDB Permissions Diagnostic Script for Audit Log Service
# Usage: ./check-dynamodb-permissions.sh [table-name]

set -e

# Configuration
REGION="eu-west-2"
ACCOUNT_ID="************"
ROLE_NAME="inndex-ecs-task-runner"
SECRET_NAME="audit-log-service/production/env-list-Wixirg"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get table name from argument or try to get from secrets
TABLE_NAME=${1:-""}

if [ -z "$TABLE_NAME" ]; then
    echo -e "${YELLOW}Getting table name from Secrets Manager...${NC}"
    TABLE_NAME=$(aws secretsmanager get-secret-value \
        --secret-id "$SECRET_NAME" \
        --region "$REGION" \
        --query 'SecretString' \
        --output text 2>/dev/null | jq -r '.TABLE_NAME' 2>/dev/null || echo "")
    
    if [ -z "$TABLE_NAME" ] || [ "$TABLE_NAME" = "null" ]; then
        echo -e "${RED}❌ Could not get TABLE_NAME from secrets. Please provide as argument.${NC}"
        echo "Usage: $0 [table-name]"
        exit 1
    fi
fi

echo -e "${YELLOW}=== DynamoDB Permissions Check for Audit Log Service ===${NC}"
echo "Table Name: $TABLE_NAME"
echo "Region: $REGION"
echo "Role: $ROLE_NAME"
echo ""

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# 1. Check if table exists
echo -e "${YELLOW}1. Checking if DynamoDB table exists...${NC}"
TABLE_STATUS=$(aws dynamodb describe-table \
    --table-name "$TABLE_NAME" \
    --region "$REGION" \
    --query 'Table.TableStatus' \
    --output text 2>/dev/null || echo "ERROR")

if [ "$TABLE_STATUS" = "ACTIVE" ]; then
    print_status 0 "DynamoDB table '$TABLE_NAME' exists and is ACTIVE"
    
    # Get table details
    ITEM_COUNT=$(aws dynamodb describe-table \
        --table-name "$TABLE_NAME" \
        --region "$REGION" \
        --query 'Table.ItemCount' \
        --output text)
    
    echo "Current item count: $ITEM_COUNT"
    
elif [ "$TABLE_STATUS" = "ERROR" ]; then
    print_status 1 "DynamoDB table '$TABLE_NAME' does not exist or is not accessible"
    echo "This could be a permissions issue or the table doesn't exist"
else
    print_status 1 "DynamoDB table '$TABLE_NAME' status: $TABLE_STATUS"
fi

# 2. Check IAM role permissions
echo -e "\n${YELLOW}2. Checking IAM role permissions...${NC}"

# Get role policy documents
ROLE_POLICIES=$(aws iam list-attached-role-policies \
    --role-name "$ROLE_NAME" \
    --query 'AttachedPolicies[].PolicyArn' \
    --output text 2>/dev/null || echo "ERROR")

if [ "$ROLE_POLICIES" != "ERROR" ]; then
    print_status 0 "IAM role '$ROLE_NAME' found"
    echo "Attached policies:"
    echo "$ROLE_POLICIES" | tr '\t' '\n'
    
    # Check for DynamoDB permissions in each policy
    echo -e "\n${YELLOW}Checking DynamoDB permissions in policies...${NC}"
    
    for policy_arn in $ROLE_POLICIES; do
        echo "Checking policy: $policy_arn"
        
        # Get policy version
        POLICY_VERSION=$(aws iam get-policy \
            --policy-arn "$policy_arn" \
            --query 'Policy.DefaultVersionId' \
            --output text 2>/dev/null)
        
        if [ -n "$POLICY_VERSION" ]; then
            # Check for DynamoDB permissions
            DYNAMODB_ACTIONS=$(aws iam get-policy-version \
                --policy-arn "$policy_arn" \
                --version-id "$POLICY_VERSION" \
                --query 'PolicyVersion.Document.Statement[?contains(Action, `dynamodb`)]' \
                --output json 2>/dev/null)
            
            if [ "$DYNAMODB_ACTIONS" != "[]" ] && [ "$DYNAMODB_ACTIONS" != "null" ]; then
                print_status 0 "Found DynamoDB permissions in $policy_arn"
                echo "$DYNAMODB_ACTIONS" | jq -r '.[].Action[]' | grep dynamodb | sort | uniq
            fi
        fi
    done
else
    print_status 1 "Could not access IAM role '$ROLE_NAME'"
fi

# 3. Test actual DynamoDB operations
echo -e "\n${YELLOW}3. Testing DynamoDB operations...${NC}"

# Test DescribeTable (read permission)
if aws dynamodb describe-table \
    --table-name "$TABLE_NAME" \
    --region "$REGION" \
    --query 'Table.TableName' \
    --output text >/dev/null 2>&1; then
    print_status 0 "DescribeTable permission works"
else
    print_status 1 "DescribeTable permission failed"
fi

# Test Scan (read permission)
if aws dynamodb scan \
    --table-name "$TABLE_NAME" \
    --region "$REGION" \
    --limit 1 \
    --query 'Count' \
    --output text >/dev/null 2>&1; then
    print_status 0 "Scan permission works"
else
    print_status 1 "Scan permission failed"
fi

# 4. Check environment variables in secrets
echo -e "\n${YELLOW}4. Checking environment variables in Secrets Manager...${NC}"

SECRET_VALUE=$(aws secretsmanager get-secret-value \
    --secret-id "$SECRET_NAME" \
    --region "$REGION" \
    --query 'SecretString' \
    --output text 2>/dev/null || echo "ERROR")

if [ "$SECRET_VALUE" != "ERROR" ]; then
    print_status 0 "Secrets Manager secret accessible"
    
    # Check required variables
    REQUIRED_VARS=("AWS_ACCESS_KEY_ID" "AWS_SECRET_ACCESS_KEY" "TABLE_NAME" "ARCHIVE_BUCKET")
    
    for var in "${REQUIRED_VARS[@]}"; do
        VALUE=$(echo "$SECRET_VALUE" | jq -r ".$var" 2>/dev/null || echo "null")
        if [ "$VALUE" != "null" ] && [ -n "$VALUE" ]; then
            print_status 0 "$var is set"
        else
            print_status 1 "$var is missing or empty"
        fi
    done
else
    print_status 1 "Cannot access Secrets Manager secret"
fi

# 5. Test with service credentials
echo -e "\n${YELLOW}5. Testing with service credentials...${NC}"

if [ "$SECRET_VALUE" != "ERROR" ]; then
    AWS_ACCESS_KEY_ID=$(echo "$SECRET_VALUE" | jq -r '.AWS_ACCESS_KEY_ID' 2>/dev/null)
    AWS_SECRET_ACCESS_KEY=$(echo "$SECRET_VALUE" | jq -r '.AWS_SECRET_ACCESS_KEY' 2>/dev/null)
    
    if [ "$AWS_ACCESS_KEY_ID" != "null" ] && [ "$AWS_SECRET_ACCESS_KEY" != "null" ]; then
        echo "Testing with service credentials..."
        
        # Test with service credentials
        if AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID" \
           AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY" \
           aws dynamodb describe-table \
           --table-name "$TABLE_NAME" \
           --region "$REGION" \
           --query 'Table.TableName' \
           --output text >/dev/null 2>&1; then
            print_status 0 "Service credentials can access DynamoDB"
        else
            print_status 1 "Service credentials cannot access DynamoDB"
        fi
        
        # Test PutItem permission (most critical)
        TEST_ITEM='{"test_key":{"S":"diagnostic_test"},"timestamp":{"N":"'$(date +%s)'"}}'
        
        if AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID" \
           AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY" \
           aws dynamodb put-item \
           --table-name "$TABLE_NAME" \
           --item "$TEST_ITEM" \
           --region "$REGION" >/dev/null 2>&1; then
            print_status 0 "PutItem permission works (test item created)"
            
            # Clean up test item
            AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID" \
            AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY" \
            aws dynamodb delete-item \
            --table-name "$TABLE_NAME" \
            --key '{"test_key":{"S":"diagnostic_test"}}' \
            --region "$REGION" >/dev/null 2>&1
            
            echo "Test item cleaned up"
        else
            print_status 1 "PutItem permission failed - THIS IS THE LIKELY ISSUE"
        fi
    else
        print_status 1 "Service credentials not found in secrets"
    fi
fi

# Summary
echo -e "\n${YELLOW}=== Summary ===${NC}"
echo "If PutItem permission failed, the audit log service cannot write to DynamoDB."
echo "This explains why there are 0 records in the table."
echo ""
echo "Required DynamoDB permissions:"
echo "- dynamodb:PutItem (CRITICAL - for creating audit logs)"
echo "- dynamodb:GetItem (for fetching existing records)"
echo "- dynamodb:Query (for retrieving logs)"
echo "- dynamodb:Scan (for listing logs)"
echo "- dynamodb:DescribeTable (for table info)"
echo ""
echo "Check the IAM policy attached to role: $ROLE_NAME"
echo "Ensure it has permissions for table: $TABLE_NAME"
