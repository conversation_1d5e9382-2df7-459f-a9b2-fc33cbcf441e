# Audit Log Service Production Troubleshooting Guide

## 1. Service Health Check

### Test the health endpoint:
```bash
# Replace with your actual production URL
curl -f https://your-audit-log-service-url/health?from=troubleshoot

# Expected response:
# {"success": true, "data": {"message": "Health check endpoint success."}}
```

### Test the root endpoint:
```bash
curl https://your-audit-log-service-url/

# Expected response:
# {"success": true, "data": {"message": "Inndex change log repo is up and running"}}
```

## 2. AWS ECS Service Status

### Check ECS service status:
```bash
aws ecs describe-services \
  --cluster inndex-prod-web-cluster \
  --services audit-log-service-service \
  --region eu-west-2
```

### Check running tasks:
```bash
aws ecs list-tasks \
  --cluster inndex-prod-web-cluster \
  --service-name audit-log-service-service \
  --region eu-west-2
```

### Check task definition:
```bash
aws ecs describe-task-definition \
  --task-definition audit-log-service-task-def \
  --region eu-west-2
```

## 3. CloudWatch Logs Investigation

### Check CloudWatch log groups:
```bash
# List log groups
aws logs describe-log-groups \
  --log-group-name-prefix "inndex-prod-audit-log-service" \
  --region eu-west-2

# Get recent logs
aws logs filter-log-events \
  --log-group-name "inndex-prod-audit-log-service" \
  --start-time $(date -d '1 hour ago' +%s)000 \
  --region eu-west-2
```

## 4. Environment Variables Check

The service requires these critical environment variables:
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY` 
- `AWS_REGION`
- `TABLE_NAME`
- `ARCHIVE_BUCKET`
- `SENTRY_DSN`
- `JWT_SECRET`

### Check AWS Secrets Manager:
```bash
aws secretsmanager get-secret-value \
  --secret-id "audit-log-service/production/env-list-Wixirg" \
  --region eu-west-2
```

## 5. DynamoDB Table Access

### Check if the DynamoDB table exists and is accessible:
```bash
# Replace TABLE_NAME with actual production table name
aws dynamodb describe-table \
  --table-name production_audit_logs \
  --region eu-west-2
```

## 6. S3 Bucket Access

### Check S3 bucket access:
```bash
# Replace ARCHIVE_BUCKET with actual bucket name
aws s3 ls s3://your-archive-bucket-name/
```

## 7. Common Issues and Solutions

### Issue 1: Service Not Starting
**Symptoms**: Health check fails, no logs in CloudWatch
**Possible Causes**:
- Missing environment variables
- DynamoDB table doesn't exist
- S3 bucket access issues
- Port binding issues

### Issue 2: Service Running but Not Processing Requests
**Symptoms**: Health check works, but API calls fail
**Possible Causes**:
- Authentication issues (JWT_SECRET)
- Database connection problems
- S3 upload failures

### Issue 3: Silent Failures
**Symptoms**: No error logs, requests seem to work but nothing is saved
**Possible Causes**:
- DynamoDB write permissions
- S3 upload permissions
- Sentry not configured properly

## 8. Manual Testing

### Test the create log endpoint:
```bash
curl -X POST https://your-audit-log-service-url/api/logs/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "key": "test_key_123",
    "event_type": "user",
    "resource_key": "test_resource",
    "action": "create",
    "origin_action": "test_controller",
    "updated_record": {"test": "data"}
  }'
```

## 9. Log Analysis Commands

### Check for specific error patterns:
```bash
# Check for AWS SDK errors
aws logs filter-log-events \
  --log-group-name "inndex-prod-audit-log-service" \
  --filter-pattern "ERROR" \
  --start-time $(date -d '1 hour ago' +%s)000

# Check for DynamoDB errors
aws logs filter-log-events \
  --log-group-name "inndex-prod-audit-log-service" \
  --filter-pattern "DynamoDB" \
  --start-time $(date -d '1 hour ago' +%s)000

# Check for S3 errors
aws logs filter-log-events \
  --log-group-name "inndex-prod-audit-log-service" \
  --filter-pattern "S3" \
  --start-time $(date -d '1 hour ago' +%s)000
```

## 10. Grafana Loki Logs (Production)

Since production uses Grafana Loki for logging, check your Grafana dashboard:
- Look for logs with label `job="prod-als-api"`
- Filter by `env="als-api"`
- Check for error-level logs

## Next Steps

1. Start with the health check (#1)
2. If health check fails, check ECS service status (#2)
3. If service is running but not working, check CloudWatch logs (#3)
4. Verify environment variables and AWS permissions (#4-6)
5. Test manually with curl commands (#8)
