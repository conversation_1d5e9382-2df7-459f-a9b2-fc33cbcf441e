// IAM Permissions Required for Audit Log Service Production
// Role: inndex-ecs-task-runner
// Service: audit-log-service
// Environment: Production

// DynamoDB Permissions (CRITICAL - Service cannot write audit logs without these)
"dynamodb:PutItem",           // Required for creating audit log entries
"dynamodb:GetItem",           // Required for fetching existing records
"dynamodb:Query",             // Required for retrieving audit logs
"dynamodb:Scan",              // Required for listing audit logs
"dynamodb:UpdateItem",        // Required for updating audit entries
"dynamodb:DeleteItem",        // Required for cleanup operations
"dynamodb:DescribeTable",     // Required for table information
"dynamodb:CreateTable",       // Required for table creation (if needed)
"dynamodb:ListTables",        // Required for table discovery

// S3 Permissions (CRITICAL - Service stores audit log archives in S3)
"s3:GetObject",               // Required for fetching old audit records
"s3:PutObject",               // Required for storing audit log archives
"s3:DeleteObject",            // Required for cleanup operations
"s3:ListBucket",              // Required for bucket operations
"s3:GetBucketLocation",       // Required for bucket access

// Secrets Manager Permissions (CRITICAL - Service gets config from secrets)
"secretsmanager:GetSecretValue",     // Required for environment variables
"secretsmanager:DescribeSecret",     // Required for secret validation

// CloudWatch Logs Permissions (Required for application logging)
"logs:CreateLogGroup",        // Required for log group creation
"logs:CreateLogStream",       // Required for log stream creation
"logs:PutLogEvents",          // Required for writing logs
"logs:DescribeLogGroups",     // Required for log group info
"logs:DescribeLogStreams",    // Required for log stream info

// RESOURCE ARNs (Replace with actual values):
// DynamoDB Table: arn:aws:dynamodb:eu-west-2:557348263238:table/YOUR_ACTUAL_TABLE_NAME
// DynamoDB Table Index: arn:aws:dynamodb:eu-west-2:557348263238:table/YOUR_ACTUAL_TABLE_NAME/*
// S3 Bucket: arn:aws:s3:::YOUR_ACTUAL_ARCHIVE_BUCKET_NAME
// S3 Objects: arn:aws:s3:::YOUR_ACTUAL_ARCHIVE_BUCKET_NAME/*
// Secrets: arn:aws:secretsmanager:eu-west-2:557348263238:secret:audit-log-service/production/env-list-Wixirg
// Logs: arn:aws:logs:eu-west-2:557348263238:log-group:inndex-prod-audit-log-service
// Logs Stream: arn:aws:logs:eu-west-2:557348263238:log-group:inndex-prod-audit-log-service:*

// TROUBLESHOOTING:
// If audit logs are not being created (0 records in DynamoDB):
// 1. Verify dynamodb:PutItem permission exists
// 2. Check resource ARN matches actual table name
// 3. Verify s3:PutObject permission for archive bucket
// 4. Ensure secretsmanager:GetSecretValue works for config

// TESTING COMMANDS:
// Test DynamoDB access: aws dynamodb describe-table --table-name YOUR_TABLE_NAME
// Test S3 access: aws s3 ls s3://YOUR_BUCKET_NAME/
// Test Secrets access: aws secretsmanager get-secret-value --secret-id audit-log-service/production/env-list-Wixirg
